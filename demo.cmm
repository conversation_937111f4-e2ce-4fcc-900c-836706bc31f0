; --------------------------------------------------------------------------------
; @Title: Demo script for SPC574Kxx
; @Description: runs on internal SRAM
; @Keywords: K2
; @Author: REI
; @Board: -
; @Chip: SPC574K*
; @Copyright: (C) 1989-2014 Lauterbach GmbH, licensed for use with TRACE32(R) only
; --------------------------------------------------------------------------------
; $Id: demo.cmm 20621 2023-03-13 10:16:05Z rweiss $


  SYStem.RESet
  
;settings for JTAG debugger / simulator
  IF SIMULATOR()
    SYStem.CPU SPC574K74
  ELSE
  (
    SYStem.DETECT.CPU
    SYStem.CONFIG.CORE 3. 1.
  )

  SYStem.Option.WATCHDOG OFF
  SYStem.BdmClock 4.MHz

;reset processor and connect
  SYStem.UP
  &use_aurora=FALSE()

;trace setup
  IF SIMULATOR()
  (
    Trace.METHOD Analyzer
    Trace.Init
  )
  ELSE IF POWERTRACE()&&!POWERNEXUS()&&CHIP.EmulationDevice()
  (
    &JTAGID=Data.Long(DBG:0x601)&0x0FFFFFFF
    IF &JTAGID==0x0AF06041
    (
      &use_aurora=FALSE()
      DIALOG.OK "Aurora NEXUS trace port unusable on K2 cut 1. Switching to On-chip trace."
    )
    ELSE
    (
      &use_aurora=TRUE() 
    )
  )
  
  IF &use_aurora
  (
    NEXUS.PortSize 2Lane
    NEXUS.PortMode 1250Mbps
    NEXUS.RefClock ON
    Trace.METHOD Analyzer 
  )
  ELSE
  (
    IF CHIP.EmulationDevice()
      Onchip.TBARange EEC:0x0C000000--0x0C0FFFFF
    ELSE
      Onchip.TBARange A:0x0D000000--0x0D003FFF

    Trace.METHOD Onchip
  )
  Trace.Init

;initialize internal SRAM
  DATA.SET EA:0x40000000--0x40007FFF %quad 0x55AA55AA55AA55AA

;load demo application - REMOVED

;execute until function main
  GO main
  WAIT !RUN()

;set debug mode to HLL debugging
  MODE.HLL

;open some windows and arrange
  WinCLEAR
  WinPOS 0.57143 0.5 80. 28. 13. 1. W000
  WinTABS 10. 10. 25. 62.
  List.auto
 
  WinPOS 85.429 0.5 77. 20. 0. 0. W004
  Register.view
 
  WinPOS 0.85714 34.25 80. 5. 0. 0. W002
  Var.View %spotlight %e flags %open vtripplearray 
 
  WinPOS 1.0 43.5 80. 12. 5. 0. W003
  Frame /Locals /Caller
 
  WinPOS 85.429 25.333 77. 13. 12. 1. W001
  WinTABS 32.
  Trace.List
 
  WinPOS 85.429 44.833 77. 11. 25. 2. W005
  Trace.Chart
 
ENDDO
