; --------------------------------------------------------------------------------
; @Title: Combined Demo and Flash script for SPC574Kxx
; @Description: Combines demo.cmm and spc574k72_Flash.cmm functionality
; @Keywords: K2
; @Author: Combined from REI scripts
; @Board: -
; @Chip: SPC574K*
; @Copyright: (C) 1989-2014 Lauterbach GmbH, licensed for use with TRACE32(R) only
; --------------------------------------------------------------------------------
; Combined from demo.cmm and spc574k72_Flash.cmm

; --------------------------------------------------------------------------------
; PART 1: Demo script functionality (without file load)
; --------------------------------------------------------------------------------

  SYStem.RESet
  
;settings for JTAG debugger / simulator
  IF SIMULATOR()
    SYStem.CPU SPC574K74
  ELSE
  (
    SYStem.DETECT.CPU
    SYStem.CONFIG.CORE 3. 1.
  )

  SYStem.Option.WATCHDOG OFF
  SYStem.BdmClock 4.MHz

;reset processor and connect
  SYStem.UP
  &use_aurora=FALSE()

;trace setup
  IF SIMULATOR()
  (
    Trace.METHOD Analyzer
    Trace.Init
  )
  ELSE IF POWERTRACE()&&!POWERNEXUS()&&CHIP.EmulationDevice()
  (
    &JTAGID=Data.Long(DBG:0x601)&0x0FFFFFFF
    IF &JTAGID==0x0AF06041
    (
      &use_aurora=FALSE()
      DIALOG.OK "Aurora NEXUS trace port unusable on K2 cut 1. Switching to On-chip trace."
    )
    ELSE
    (
      &use_aurora=TRUE() 
    )
  )
  
  IF &use_aurora
  (
    NEXUS.PortSize 2Lane
    NEXUS.PortMode 1250Mbps
    NEXUS.RefClock ON
    Trace.METHOD Analyzer 
  )
  ELSE
  (
    IF CHIP.EmulationDevice()
      Onchip.TBARange EEC:0x0C000000--0x0C0FFFFF
    ELSE
      Onchip.TBARange A:0x0D000000--0x0D003FFF

    Trace.METHOD Onchip
  )
  Trace.Init

;initialize internal SRAM
  DATA.SET EA:0x40000000--0x40007FFF %quad 0x55AA55AA55AA55AA

;load demo application - REMOVED

;execute until function main
  GO main
  WAIT !RUN()

;set debug mode to HLL debugging
  MODE.HLL

;open some windows and arrange
  WinCLEAR
  WinPOS 0.57143 0.5 80. 28. 13. 1. W000
  WinTABS 10. 10. 25. 62.
  List.auto
 
  WinPOS 85.429 0.5 77. 20. 0. 0. W004
  Register.view
 
  WinPOS 0.85714 34.25 80. 5. 0. 0. W002
  Var.View %spotlight %e flags %open vtripplearray 
 
  WinPOS 1.0 43.5 80. 12. 5. 0. W003
  Frame /Locals /Caller
 
  WinPOS 85.429 25.333 77. 13. 12. 1. W001
  WinTABS 32.
  Trace.List
 
  WinPOS 85.429 44.833 77. 11. 25. 2. W005
  Trace.Chart

; --------------------------------------------------------------------------------
; PART 2: Flash script functionality
; --------------------------------------------------------------------------------

; reference AMP script for debug of cores Z2, Z4 and GTM of SPC574K (K2)
;   Besides the main GUI, two more TRACE32 GUIs are opened and configured

;   script-local macros:
LOCAL &portMZ2 &portMZ4 &portGTM &addressMZ2 &addressMZ4 &addressGTM

LOCAL &DualPort
IF VERSION.BUILD.BASE()>=45520.
&DualPort="/DualPort"

; sanity check:
IF CPUFAMILY()!="POWERPC"
(
  DIALOG.OK "Run this script on the PowerPC GUI"
  ENDDO
)

; intercom setup:
GOSUB setup_intercom

; set up helper commands
ON CMD COREMZ2 GOSUB COREMZ2
ON CMD COREMZ4 GOSUB COREMZ4
ON CMD COREGTM GOSUB coreGTM
ON CMD COREALL GOSUB coreAll

; check connection / launch GUI:
GOSUB other_gui

; add button, which when pressed closes all PowerView instances
;DO "~~/demo/practice/intercom/toolbar_quit_all.cmm"

; clear all windows
COREALL WinCLEAR

; initialize and start the debugger
COREGTM RESet
COREMZ2 RESet
COREMZ4 RESet
COREALL SYStem.CPU SPC574K72
; mount all cores into the same chip:
COREMZ2 SYStem.CONFIG CORE 3. 1.
COREMZ4 SYStem.CONFIG CORE 1. 1.
COREGTM SYStem.CONFIG CORE 5. 1.
COREALL SYStem.CONFIG.TAPState 12.
COREALL Break.IMPLementation Program ONCHIP 

COREGTM SYStem.CONFIG.MCSModule MCS0
;COREGTM SYStem.CONFIG.MCSModule MCS1
;COREGTM SYStem.CONFIG.MCSModule MCS2
COREGTM CORE.select 0
COREMZ2 SYStem.Up
COREMZ2 DO .\win_c2.cmm
COREMZ4 DO .\win_c0.cmm
;COREGTM DO .\win_gtm.cmm

; optional settings:
COREALL SETUP.VAR %SpotLight

if CHIP.EmulationDevice()
(
  ;Onchip Trace Emulation Device
  COREMZ2 Onchip.TBAddress EEC:0x0C000000--0x0C0FFFFF
  COREMZ2 Onchip.Init
  COREMZ4 Onchip.TBAddress EEC:0x0C000000--0x0C0FFFFF
  COREMZ4 Onchip.Init
)
else
(
  ;Onchip Trace Production Device
  COREMZ2 Onchip.TBARange A:0x0d000000--0x0d003fff 
  COREMZ2 Onchip.Init
  COREMZ4 Onchip.TBARange A:0x0d000000--0x0d003fff 
  COREMZ4 Onchip.Init
)

; Main SRAM 128KB SRAM
COREMZ2 Data.Set EA:0x40000000--0x4000FFFF %BE %QUAD 0x0

; Flash preparation
;DO ~~\demo\powerpc\flash\jpc574xk.cmm PREPAREONLY SKIPCONFIG
DO jpc574xk.cmm PREPAREONLY SKIPCONFIG

; Load application  (MPC and GTM)
;intram.elf /verify /strippart 8 /sourcepath J:\RAI\GTM\MPC5746M_STARTUP_RAM_WR
;COREMZ2 Data.LOAD intram.elf /verify /strippart 8 

; Flash programming

DIALOG.YESNO "Flash programming prepared. Program flash memory now?"
LOCAL &progflash
ENTRY &progflash

IF &progflash 
(

      ;TEST ERASE
    FLASH.Erase 0x00FF0000--0x0123FFFF

    ;DATA FLASH ERASE
    FLASH.Erase 0x800000--0x80FFFF

    FLASH.ReProgram 0x00FF0000--0x011FFFFF /Erase
    ;Data.LOAD.binary ../GHS/bin/I6C61D_EVB/I6C61D_EVB-appl_trusted.bin 0x00FF0000--0x011FFFFF
    ;Data.LOAD.binary ../GHS/bin/I6C61D/I6C61D-appl_trusted.bin 0x00FF0000--0x011FFFFF
    Data.LOAD.binary ../GHS/bin/M3E37D/M3E37D-appl_trusted.bin 0x00FF0000--0x011FFFFF
    FLASH.ReProgram.off 
)
ELSE
(
    ;DATA FLASH ERASE
    ;FLASH.Erase 0x800000--0x80FFFF
    ;FLASH.ReProgram 0x800000--0x80FFFF /Erase
    ;Data.LOAD.binary EE_Appl_Corrupt_Descr_Page0.bin 0x800000--0x80FFFF
    ;Data.LOAD.binary EE_Appl_Corrupt_Descr_Page1.bin 0x800000--0x80FFFF
    ;Data.LOAD.binary EE_Appl_Corrupt_Data_Page0.bin 0x800000--0x80FFFF
    ;Data.LOAD.binary EE_Appl_Corrupt_Data_Page1.bin 0x800000--0x80FFFF   
    ;FLASH.ReProgram.off 

    ;TEST ERASE
    ;FLASH.Erase 0x00FF0000--0x0123FFFF
    
    ;DATA FLASH ERASE
    ;FLASH.Erase 0x800000--0x80FFFF
    ;FLASH.List
    
    ;ERASE ALL
    ;FLASH.Erase ALL
)

;Data.LOAD.elf ../GHS/bin/I6C61D_EVB/I6C61D_EVB.elf 0x00FF0000--0x011FFFFF /GHS /NOCODE
;Data.LOAD.elf ../GHS/bin/I6C61D/I6C61D.elf 0x00FF0000--0x011FFFFF /GHS /NOCODE
Data.LOAD.elf ../GHS/bin/M3E37D/M3E37D.elf 0x00FF0000--0x011FFFFF /GHS /NOCODE

; Attach to GTM and enable GTM 
COREGTM SYStem.Mode Attach
COREMZ2 Go app_start\7
COREGTM Break
COREGTM Data.LOAD.elf ../GHS/bin/M3E37D/M3E37D.elf 0x00FF0000--0x011FFFFF /GHS /NOCODE ;/NoRegister
; Attach to core Z4
COREMZ4 SYStem.Mode Attach
COREMZ4 Break
COREMZ4 Data.LOAD.elf ../GHS/bin/M3E37D/M3E37D.elf 0x00FF0000--0x011FFFFF /GHS /NOCODE ;/NoRegister
;MC Break on mains
;COREMZ2 Go mainCore2
COREMZ2 Go appManagerCore2
COREMZ4 Go __start_c0
;COREMZ4 Go Gtm_Eisb_Config

; TrOnchip setting for GTM - Breakpoints for all cores
COREGTM TrOnchip.WPCE 0xFF
; EVTO1 shows the ARU access
COREGTM TrOnchip.EVTO1 ARU 
COREGTM TrOnchip.EVTO0 ARU 

; Nexus setting for GTM - Program and Data Trace for Channel 0 and 1, ARU Trace
COREGTM NEXUS.FTM ON
COREGTM NEXUS.FTCE 0x03
COREGTM NEXUS.DTM ReadWrite
COREGTM NEXUS.DTCE 0x03
COREGTM NEXUS.ARU ON
COREGTM NEXUS.ARUAccess0 0x77
COREGTM NEXUS.ARUAccess1 0x78
COREGTM NEXUS.TimeStamps ON

; Trace Settings for GTM
COREGTM Trace.METHOD Onchip
COREGTM Onchip.CLOCK 160.MHZ

; Nexus setting for core Z2
COREMZ2 NEXUS.BTM ON
COREMZ2 Trace.METHOD Onchip
COREMZ2 Onchip.AutoInit ON
;EVTO Setting EVTO1 on PF[15] and EVTO0 on PF[14]
COREMZ2 NEXUS.PINCR 0x8100
COREMZ2 PER.Set.Field DBG:0x60E %L 0x00000008 0x1 ; DCI_CR EVTO1 enabled
COREMZ2 PER.Set.Field DBG:0x60E %L 0x00000004 0x1 ; DCI_CR EVTO0 enabled

; Nexus setting for core Z4
COREMZ4 NEXUS.BTM ON
COREMZ4 Trace.METHOD Onchip
COREMZ4 Onchip.AutoInit ON

; Synch setting for MPC and GTM
COREALL SYnch.RESet
COREALL SYnch.ON
COREMZ2 SYnch.Connect &addressMZ4 &addressGTM
COREMZ4 SYnch.Connect &addressMZ2 &addressGTM
COREGTM SYnch.Connect &addressMZ2 &addressMZ4
COREALL SYnch.MasterGo ON
COREALL SYnch.MasterBreak ON
COREALL SYnch.SlaveGo ON
COREALL SYnch.SlaveBreak ON
COREALL SYnch.MasterSystemMode ON
COREALL SYnch.SlaveSystemMode ON
COREMZ2 SYnch.XTrack &addressMZ4 &addressGTM
COREMZ4 SYnch.XTrack &addressMZ2 &addressGTM
COREGTM SYnch.XTrack &addressMZ2 &addressMZ4
COREMZ2 SYStem.CONFIG Slave OFF
COREMZ4 SYStem.CONFIG Slave ON
COREGTM SYStem.CONFIG Slave ON

; arrange GUIs and open some windows
COREMZ2 FramePOS 0% 0% 50% 75%
COREMZ4 FramePOS 0% 0% 50% 75%

;COREMZ2 WinPOS 0% 0% 50% 100%
;COREMZ2 List.auto
;COREMZ2 WinPOS 50% 0% 50% 100%
;COREMZ2 Trace.List /Track

COREMZ4 WinPOS 0% 0% 50% 100%
COREMZ4 List.auto

COREGTM FramePOS 50% 0% 50% 75%

COREGTM WinPOS 0% 0% 50% 65%
COREGTM List.auto /CORE 0

COREGTM WinPOS 50% 0% 50% 65%
COREGTM Trace.List /Track

COREGTM WinPOS 0% 70%
COREGTM Register /CORE 0

COREGTM WinPOS 50% 70% 
COREGTM NEXUS 

ENDDO

; --------------------------------------------------------------------------------
; helper subroutines:

COREMZ2:
(
  LOCAL &params
  ENTRY %Line &params
  &params ; execute on this GUI
  RETURN
)

COREMZ4:
(
  LOCAL &params
  ENTRY %Line &params
  INTERCOM.execute &addressMZ4 &params ; execute on remote GUI
  RETURN
)

coreGTM:
(
  LOCAL &params
  ENTRY %Line &params
  INTERCOM.execute &addressGTM &params ; execute on remote GUI
  RETURN
)

coreAll:
(
  LOCAL &params
  ENTRY %Line &params
  GOSUB COREMZ2 &params
  GOSUB COREMZ4 &params
  GOSUB coreGTM &params
  RETURN
)

setup_intercom:
(
  &portMZ2=FORMAT.DECIMAL(1.,INTERCOM.PORT())
  &portMZ4=FORMAT.DECIMAL(1.,INTERCOM.PORT()+1.)
  &portGTM=FORMAT.DECIMAL(1.,INTERCOM.PORT()+2.)
  &addressMZ2="127.0.0.1:&portMZ2"
  &addressMZ4="127.0.0.1:&portMZ4"
  &addressGTM="127.0.0.1:&portGTM"
  RETURN
)

other_gui:
(
  LOCAL &nodename &launchGUI &p3_tmp &p4_sys &p5_help &p6_pbi &p7_opt &p8_opt
  &p3_tmp=OS.PTD()
  &p4_sys=OS.PSD()
  &p5_help=OS.PHELPD()

  &nodename=NODENAME()
  IF "&nodename"=="" // no IP address to debug interface -> USB connection
    &p6_pbi="USB"
  ELSE
  (
    &p6_pbi="NET"
    &p7_opt="NODE=&nodename"
    &p8_opt="PACKLEN=1024"
  )

  WAIT INTERCOM.PING(&addressMZ4) 3.s
  IF !INTERCOM.PING(&addressMZ4)
  (
    PRINT "no debugger / GUI at &addressMZ4 detected, launching second GUI..."
    &launchGUI=OS.PED()+"/t32mppc"+" -c "+OS.PPD()+"/config_multicore.t32 &portMZ4 core_Z4 &p3_tmp &p4_sys &p5_help &p6_pbi &p7_opt &p8_opt CORE=2"
    OS.screen &launchGUI
  )
  WAIT INTERCOM.PING(&addressMZ4) 5.s

  WAIT INTERCOM.PING(&addressGTM) 3.s
  IF !INTERCOM.PING(&addressGTM)
  (
    PRINT "no debugger / GUI at &addressGTM detected, launching the GUI..."
    &launchGUI=OS.PED()+"/t32mgtm"+" -c "+OS.PPD()+"/config_multicore.t32 &portGTM GTM &p3_tmp &p4_sys &p5_help &p6_pbi &p7_opt &p8_opt CORE=3"
    OS.screen &launchGUI
  )
  WAIT INTERCOM.PING(&addressGTM) 5.s
  RETURN
)
